# 文字宽度修复说明

## 问题描述

在文字识别后，由于文字block的宽度计算不准确，导致文字在渲染时出现不必要的换行问题。

## 问题原因

1. **原始宽度计算不准确**: OCR识别返回的边界框宽度可能过小，不足以容纳完整的文字内容
2. **缺少文字宽度估算**: 没有根据文字内容和字体大小来合理估算所需宽度
3. **没有最小宽度保证**: 短文字可能得到过小的宽度值

## 修复方案

### 1. 添加文字宽度计算函数

在 `dataConverter.js` 和 `llmDataConverter.js` 中添加了 `calculateTextWidth` 方法：

```javascript
calculateTextWidth(text, originalWidth, fontSize = 12.0) {
  if (!text || text.length === 0) {
    return Math.max(originalWidth, 20); // 最小宽度20
  }

  // 估算字符宽度：中文字符约等于字体大小，英文字符约为字体大小的0.6倍
  const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
  const otherCharCount = text.length - chineseCharCount;
  
  const estimatedWidth = chineseCharCount * fontSize + otherCharCount * fontSize * 0.6;
  
  // 取原始宽度和估算宽度的较大值，并添加一些边距
  const adjustedWidth = Math.max(originalWidth, estimatedWidth * 1.1);
  
  // 设置最小和最大宽度限制
  const minWidth = 20;
  const maxWidth = this.canvasWidth * 0.8; // 不超过画布宽度的80%
  
  return Math.max(minWidth, Math.min(maxWidth, adjustedWidth));
}
```

### 2. 修改文本转换逻辑

在 `convertTextLine` 方法中应用新的宽度计算：

```javascript
convertTextLine(textLine) {
  const bounds = this.calculateBounds(textLine.pos);
  const text = textLine.text || '';
  
  // 计算合理的文字宽度，避免不必要的换行
  const adjustedWidth = this.calculateTextWidth(text, bounds.width, 12.0);

  return {
    // ... 其他属性
    width: adjustedWidth.toString(),
    content: text,
    lineWrap: 'true'
  };
}
```

### 3. 宽度计算策略

- **中文字符**: 宽度约等于字体大小
- **英文字符**: 宽度约为字体大小的0.6倍
- **边距处理**: 在估算宽度基础上增加10%的边距
- **最小宽度**: 保证至少20像素的最小宽度
- **最大宽度**: 不超过画布宽度的80%

## 测试验证

### 测试用例

1. **短文字**: "短文字" - 验证最小宽度保证
2. **中等长度**: "这是中等长度的文字内容" - 验证中文宽度计算
3. **长文字**: "这是一段很长很长的中文文字内容..." - 验证长文字处理
4. **英文文字**: "Short English text" - 验证英文宽度计算
5. **中英混合**: "中英混合 Mixed text 测试" - 验证混合文字处理

### 测试页面

创建了 `test-text-width.html` 页面，可以直观地对比修复前后的效果：
- 红色边框: 原始宽度
- 绿色边框: 调整后宽度

## 预期效果

1. **减少换行**: 文字内容在合理的宽度内显示，避免不必要的换行
2. **保持布局**: 不会因为宽度过大而破坏整体布局
3. **适应性强**: 能够处理中文、英文和混合文字的不同情况
4. **性能优化**: 计算逻辑简单高效，不影响识别性能

## 兼容性

- 保持与XPrinter格式的完全兼容
- 不影响其他元素类型（条形码、图片等）的处理
- 向后兼容现有的识别结果

## 使用方法

修复已自动应用到以下场景：
1. TextIn API识别结果转换
2. 大模型识别结果转换
3. 所有文字元素的宽度计算

无需额外配置，系统会自动使用新的宽度计算逻辑。
