import React, { useState } from 'react';
import { Card, Typography, Space, Tag, Divider, Input, message, Switch } from 'antd';
import { QRCodeSVG } from 'qrcode.react';
import BarcodeRenderer from './BarcodeRenderer';
import EditableText from './EditableText';

const { Title, Text, Paragraph } = Typography;

// 元素类型映射
const ELEMENT_TYPE_NAMES = {
  '1': '文本',
  '2': '条形码',
  '3': '画布',
  '4': '线条',
  '5': 'Logo',
  '6': '图片',
  '7': '二维码',
  '8': '圆形',
  '9': '时间',
  '10': '表格',
  '11': '矩形'
};

const LabelRenderer = ({ data, canvasWidth = 800, canvasHeight = 600, onDataChange }) => {
  const [localData, setLocalData] = useState(data || []);
  const [showElementList, setShowElementList] = useState(false); // 控制是否显示元素列表

  // 当外部数据变化时更新本地数据
  React.useEffect(() => {
    setLocalData(data || []);
  }, [data]);

  // 更新元素内容
  const updateElementContent = (index, newContent) => {
    const newData = [...localData];
    newData[index] = { ...newData[index], content: newContent };
    setLocalData(newData);

    // 通知父组件数据变化
    if (onDataChange) {
      onDataChange(newData);
    }
  };

  if (!localData || !Array.isArray(localData) || localData.length === 0) {
    return (
      <Card title="标签预览">
        <Text type="secondary">暂无数据</Text>
      </Card>
    );
  }

  // 查找画布元素
  const canvasElement = localData.find(item => item.elementType === '3' || item.elementType === 3);
  const actualCanvasWidth = canvasElement ? parseInt(canvasElement.width) || canvasWidth : canvasWidth;
  const actualCanvasHeight = canvasElement ? parseInt(canvasElement.height) || canvasHeight : canvasHeight;

  console.log('画布元素:', canvasElement);
  console.log('实际画布尺寸:', actualCanvasWidth, 'x', actualCanvasHeight);

  // 移除缩放逻辑，直接使用原始尺寸以实现1:1渲染
  // 原始缩放逻辑已被注释掉，以实现所见即所得
  /*
  const maxDisplayWidth = 800;
  const maxDisplayHeight = 600;
  const scaleX = Math.min(maxDisplayWidth / actualCanvasWidth, 1);
  const scaleY = Math.min(maxDisplayHeight / actualCanvasHeight, 1);
  const scale = Math.min(scaleX, scaleY);
  */
  const scale = 1; // 强制1:1渲染
  const displayWidth = actualCanvasWidth;
  const displayHeight = actualCanvasHeight;

  // 添加详细的调试信息（移动到scale定义之后）
  console.log('=== 详细调试信息 ===');
  console.log('原始数据:', localData);
  console.log('画布尺寸:', { actualCanvasWidth, actualCanvasHeight });
  console.log('缩放比例:', scale);
  console.log('显示尺寸:', { displayWidth, displayHeight });

  // 分析每个文本元素的坐标
  const textElements = localData.filter(item => String(item.elementType) === '1');
  console.log('文本元素分析:');
  textElements.forEach((element, index) => {
    console.log(`文本${index + 1}: "${element.content}"`);
    console.log(`  原始坐标: (${element.x}, ${element.y})`);
    console.log(`  原始尺寸: ${element.width} x ${element.height}`);
    console.log(`  字体大小: ${element.textSize}`);
    console.log(`  渲染坐标: (${parseInt(element.x) * scale}, ${parseInt(element.y) * scale})`);
    console.log(`  渲染尺寸: ${parseInt(element.width) * scale} x ${parseInt(element.height) * scale}`);
    console.log('---');
  });

  // 过滤掉画布元素，只渲染其他元素
  const renderElements = localData.filter(item => item.elementType !== '3' && item.elementType !== 3);

  const renderElement = (element, renderIndex) => {
    // 找到元素在原始数据中的真实索引
    const actualIndex = localData.findIndex(item => item === element);
    const x = (parseInt(element.x) || 0) * scale;
    const y = (parseInt(element.y) || 0) * scale;
    const width = (parseInt(element.width) || 100) * scale;
    const height = (parseInt(element.height) || 20) * scale;
    const rotation = parseInt(element.rotationAngle) || 0;

    // 使用元素的实际字体大小，并根据缩放比例调整
    const actualFontSize = parseFloat(element.textSize || '12.0') * scale;
    const fontSize = Math.max(8, actualFontSize); // 最小字体大小8px

    const baseStyle = {
      position: 'absolute',
      left: `${x}px`,
      top: `${y}px`,
      width: `${width}px`,
      height: `${height}px`,
      transform: rotation !== 0 ? `rotate(${rotation}deg)` : 'none',
      transformOrigin: 'top left',
      border: '1px dashed #ccc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-start', // 简单使用左对齐，位置由坐标决定
      fontSize: `${fontSize}px`,
      overflow: 'hidden',
      boxSizing: 'border-box'
    };

    // 添加调试日志
    console.log(`渲染元素: 类型=${element.elementType}, 内容=${element.content}, 索引=${renderIndex}`);
    console.log(`  原始坐标: (${element.x}, ${element.y}), 原始尺寸: ${element.width}x${element.height}`);
    console.log(`  缩放后坐标: (${x}, ${y}), 缩放后尺寸: ${width}x${height}, 缩放比例: ${scale}`);

    switch (String(element.elementType)) {
      case '1': // 文本
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(24, 144, 255, 0.1)',
              borderColor: '#1890ff',
              padding: '2px',
              textAlign: 'left', // 简单使用左对齐
              fontWeight: element.bold === 'true' ? 'bold' : 'normal',
              fontStyle: element.italic === 'true' ? 'italic' : 'normal',
              textDecoration: element.underline === 'true' ? 'underline' : 'none'
            }}
            title={`文本: ${element.content || '空文本'} 位置:(${element.x}, ${element.y}) 尺寸:${element.width}x${element.height} (点击编辑)`}
          >
            <EditableText
              value={element.content || ''}
              onChange={(newContent) => updateElementContent(actualIndex, newContent)}
              placeholder="点击编辑文本"
              style={{
                fontSize: 'inherit',
                fontWeight: 'inherit',
                fontStyle: 'inherit',
                textDecoration: 'inherit',
                textAlign: 'left',
                width: '100%'
              }}
              textStyle={{
                fontSize: 'inherit',
                lineHeight: '1.2',
                wordBreak: 'normal',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            />
          </div>
        );

      case '2': // 条形码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(82, 196, 26, 0.1)',
              borderColor: '#52c41a',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title={`条形码: ${element.content || '123456789'} (点击编辑)`}
          >
            <div style={{ width: '100%', height: '100%', position: 'relative' }}>
              <BarcodeRenderer
                value={element.content || '123456789'}
                format={element.barcodeType || '1'}
                width={Math.max(1, Math.floor(width / 80))}
                height={Math.max(30, Math.floor(height * 0.7))}
                displayValue={element.showText !== 'false'}
                fontSize={Math.max(8, Math.floor(fontSize * 0.8))}
                textAlign={element.textAlignment === '1' ? 'left' :
                  element.textAlignment === '3' ? 'right' : 'center'}
                style={{
                  width: '100%',
                  height: '100%'
                }}
              />
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  cursor: 'pointer',
                  background: 'transparent',
                  zIndex: 1
                }}
                onClick={() => {
                  const newContent = prompt('请输入条码内容:', element.content || '123456789');
                  if (newContent !== null && newContent !== element.content) {
                    updateElementContent(index, newContent);
                  }
                }}
              />
            </div>
          </div>
        );

      case '7': // 二维码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(250, 173, 20, 0.1)',
              borderColor: '#faad14',
              padding: '4px',
              position: 'relative'
            }}
            title={`二维码: ${element.content || 'QR Code'} (点击编辑)`}
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {width > 30 && height > 30 ? (
                <QRCodeSVG
                  value={element.content || 'QR Code'}
                  size={Math.min(width - 8, height - 8)}
                  level="M"
                />
              ) : (
                <div style={{
                  width: '80%',
                  height: '80%',
                  backgroundColor: '#000',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '6px'
                }}>
                  QR
                </div>
              )}
            </div>
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                cursor: 'pointer',
                background: 'transparent'
              }}
              onClick={() => {
                const newContent = prompt('请输入二维码内容:', element.content || 'QR Code');
                if (newContent !== null && newContent !== element.content) {
                  updateElementContent(index, newContent);
                }
              }}
            />
          </div>
        );

      case '6': // 图片
        console.log(`渲染图片元素: 内容=${element.content ? element.content.substring(0, 50) + '...' : '无内容'}`);
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(114, 46, 209, 0.1)',
              borderColor: '#722ed1'
            }}
            title="图片"
          >
            {element.content && element.content.startsWith('data:image') ? (
              <img
                src={element.content}
                alt="识别的图片"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            ) : (
              // 如果没有有效图片内容，显示空白而不是"图片"文字
              <div style={{ width: '100%', height: '100%', backgroundColor: 'transparent' }} />
            )}
          </div>
        );

      case '10': // 表格
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(235, 47, 6, 0.1)',
              borderColor: '#eb2f06',
              padding: '2px'
            }}
            title="表格"
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(20px, 1fr))',
              gridTemplateRows: 'repeat(auto-fit, minmax(10px, 1fr))',
              gap: '1px',
              backgroundColor: '#ccc'
            }}>
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={i}
                  style={{
                    backgroundColor: 'white',
                    fontSize: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {i < 3 ? 'H' : 'D'}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(140, 140, 140, 0.1)',
              borderColor: '#8c8c8c'
            }}
            title={`${ELEMENT_TYPE_NAMES[element.elementType] || '未知类型'}`}
          >
            <Text style={{ fontSize: 'inherit' }}>
              {ELEMENT_TYPE_NAMES[element.elementType] || '未知'}
            </Text>
          </div>
        );
    }
  };

  // 主渲染区域样式
  const previewAreaStyle = {
    border: '1px solid #d9d9d9',
    position: 'relative',
    overflow: 'auto', // 当内容超出时显示滚动条
    maxWidth: 800, // 限制最大宽度
    maxHeight: '70vh', // 限制最大高度，避免过高
    backgroundColor: '#f5f5f5',
    margin: '0 auto', // 居中显示
  };

  const canvasStyle = {
    position: 'relative',
    width: `${displayWidth}px`,
    height: `${displayHeight}px`,
    backgroundColor: 'white',
    boxShadow: '0 0 10px rgba(0,0,0,0.1)',
    margin: 'auto' // 在滚动容器内居中
  };

  return (
    <Card title="标签预览" style={{ width: '100%' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 画布信息 */}
        <div>
          <Text strong>画布尺寸: </Text>
          <Text>{actualCanvasWidth} × {actualCanvasHeight} px</Text>
          <Text type="secondary" style={{ marginLeft: 16 }}>
            显示比例: {(scale * 100).toFixed(1)}%
          </Text>
        </div>

        {/* 元素统计 */}
        <div>
          <Text strong>元素统计: </Text>
          <Space wrap>
            {Object.entries(
              renderElements.reduce((acc, item) => {
                const typeName = ELEMENT_TYPE_NAMES[item.elementType] || '未知';
                acc[typeName] = (acc[typeName] || 0) + 1;
                return acc;
              }, {})
            ).map(([type, count]) => (
              <Tag key={type} color="blue">
                {type}: {count}
              </Tag>
            ))}
          </Space>
        </div>

        <Divider />

        {/* 画布渲染区域 */}
        <div style={previewAreaStyle}>
          <div style={canvasStyle}>
            {renderElements.map((element, index) => renderElement(element, index))}
          </div>

          {renderElements.length === 0 && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center'
              }}
            >
              <Text type="secondary">暂无识别到的元素</Text>
            </div>
          )}
        </div>

        {/* 元素列表 */}
        {renderElements.length > 0 && (
          <Card size="small" title="元素列表">
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              {renderElements.map((element, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    backgroundColor: '#fafafa'
                  }}
                >
                  <Space>
                    <Tag color="blue">
                      {ELEMENT_TYPE_NAMES[element.elementType] || '未知'}
                    </Tag>
                    <Text>
                      位置: ({element.x}, {element.y})
                    </Text>
                    <Text>
                      尺寸: {element.width} × {element.height}
                    </Text>
                    {element.content && (
                      <Text type="secondary">
                        内容: {element.content.length > 20
                          ? element.content.substring(0, 20) + '...'
                          : element.content}
                      </Text>
                    )}
                  </Space>
                </div>
              ))}
            </Space>
          </Card>
        )}
      </Space>
    </Card>
  );
};

export default LabelRenderer;
