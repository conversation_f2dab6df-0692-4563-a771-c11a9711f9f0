import React, { useState, useEffect } from 'react';
import { Layout, Typography, Space, Card, Row, Col, Button, Modal, Input, Form, message, Divider, Alert, Tabs, Select, Radio } from 'antd';
import { SettingOutlined, InfoCircleOutlined, GithubOutlined, BarcodeOutlined, RobotOutlined } from '@ant-design/icons';
import ImageUpload from './components/ImageUpload';
import LabelRenderer from './components/LabelRenderer';
import BarcodeTest from './components/BarcodeTest';
import textInApi from './services/textinApi';
import dataConverter from './services/dataConverter';
import llmVisionApi from './services/llmVisionApi';
import llmDataConverter from './services/llmDataConverter';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

function App() {
  const [loading, setLoading] = useState(false);
  const [recognitionResult, setRecognitionResult] = useState(null);
  const [convertedData, setConvertedData] = useState(null);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [apiMode, setApiMode] = useState('textin'); // 'textin' 或 'llm'
  const [credentials, setCredentials] = useState({
    appId: '',
    secretCode: ''
  });
  const [llmConfig, setLlmConfig] = useState({
    provider: 'spark',
    apiKey: '',
    sparkAuth: {
      appId: '4ada222d',
      apiSecret: 'ZWI2YjY2OTQ0NjcyZjNlMWQ3ODlhNzM4',
      apiKey: '6cbe2b0468fce889711ae469250a47ca'
    }
  });

  // 立即设置默认的星火认证信息
  React.useEffect(() => {
    console.log('立即设置默认星火认证信息');
    llmVisionApi.setSparkAuth(
      '4ada222d',
      'ZWI2YjY2OTQ0NjcyZjNlMWQ3ODlhNzM4',
      '6cbe2b0468fce889711ae469250a47ca'
    );
  }, []);

  // 从localStorage加载配置
  useEffect(() => {
    // 加载TextIn凭据
    const savedCredentials = localStorage.getItem('textin-credentials');
    if (savedCredentials) {
      try {
        const parsed = JSON.parse(savedCredentials);
        setCredentials(parsed);
        textInApi.setCredentials(parsed.appId, parsed.secretCode);
      } catch (error) {
        console.error('加载TextIn凭据失败:', error);
      }
    }

    // 加载大模型配置
    const savedLlmConfig = localStorage.getItem('llm-config');
    if (savedLlmConfig) {
      try {
        const parsed = JSON.parse(savedLlmConfig);
        setLlmConfig(parsed);

        if (parsed.provider === 'spark' && parsed.sparkAuth) {
          llmVisionApi.setSparkAuth(
            parsed.sparkAuth.appId,
            parsed.sparkAuth.apiSecret,
            parsed.sparkAuth.apiKey
          );
        } else {
          llmVisionApi.setConfig(parsed.provider, parsed.apiKey);
        }
      } catch (error) {
        console.error('加载大模型配置失败:', error);
      }
    }

    // 加载API模式
    const savedApiMode = localStorage.getItem('api-mode');
    if (savedApiMode) {
      setApiMode(savedApiMode);
    }
  }, []);

  // 单独的useEffect来设置默认的星火认证信息
  useEffect(() => {
    // 如果没有保存的配置，使用默认的星火认证信息
    const savedLlmConfig = localStorage.getItem('llm-config');
    if (!savedLlmConfig && llmConfig.sparkAuth) {
      console.log('设置默认星火认证信息:', llmConfig.sparkAuth);
      llmVisionApi.setSparkAuth(
        llmConfig.sparkAuth.appId,
        llmConfig.sparkAuth.apiSecret,
        llmConfig.sparkAuth.apiKey
      );
    } else if (savedLlmConfig) {
      // 如果有保存的配置，确保也设置到API中
      try {
        const parsed = JSON.parse(savedLlmConfig);
        if (parsed.provider === 'spark' && parsed.sparkAuth) {
          console.log('重新设置保存的星火认证信息:', parsed.sparkAuth);
          llmVisionApi.setSparkAuth(
            parsed.sparkAuth.appId,
            parsed.sparkAuth.apiSecret,
            parsed.sparkAuth.apiKey
          );
        }
      } catch (error) {
        console.error('重新设置保存的配置失败:', error);
      }
    }
  }, [llmConfig]);

  const handleUploadSuccess = async (result, file) => {
    console.log('识别成功:', result);
    setRecognitionResult(result);

    try {
      // 根据API模式选择转换器
      console.log('开始转换数据格式...');
      let converted;

      if (apiMode === 'llm') {
        converted = llmDataConverter.convertToXPrinter(result);
      } else {
        converted = await dataConverter.convertToXPrinter(result);
      }

      setConvertedData(converted);
      console.log('转换后的数据:', converted);
    } catch (error) {
      console.error('数据转换失败:', error);
      message.error(`数据转换失败: ${error.message}`);
    }
  };

  const handleUploadError = (error) => {
    console.error('识别失败:', error);
    setRecognitionResult(null);
    setConvertedData(null);
  };

  const handleDataChange = (newData) => {
    console.log('数据已更新:', newData);
    setConvertedData(newData);
  };

  const handleSaveCredentials = (values) => {
    try {
      if (values.appId !== undefined) {
        // 保存TextIn凭据
        setCredentials(values);
        textInApi.setCredentials(values.appId, values.secretCode);
        localStorage.setItem('textin-credentials', JSON.stringify(values));
      }

      if (values.provider !== undefined) {
        // 保存大模型配置
        const newLlmConfig = {
          provider: values.provider,
          apiKey: values.llmApiKey || '',
          sparkAuth: values.provider === 'spark' ? {
            appId: values.sparkAppId || llmConfig.sparkAuth.appId,
            apiSecret: values.sparkApiSecret || llmConfig.sparkAuth.apiSecret,
            apiKey: values.sparkApiKey || llmConfig.sparkAuth.apiKey
          } : llmConfig.sparkAuth
        };

        setLlmConfig(newLlmConfig);

        if (values.provider === 'spark') {
          llmVisionApi.setSparkAuth(
            newLlmConfig.sparkAuth.appId,
            newLlmConfig.sparkAuth.apiSecret,
            newLlmConfig.sparkAuth.apiKey
          );
        } else {
          llmVisionApi.setConfig(values.provider, values.llmApiKey);
        }

        localStorage.setItem('llm-config', JSON.stringify(newLlmConfig));
      }

      setSettingsVisible(false);
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    }
  };

  const handleApiModeChange = (mode) => {
    setApiMode(mode);
    localStorage.setItem('api-mode', mode);
  };

  const downloadJson = (data, filename) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        background: '#fff',
        padding: '0 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            图像识别测试平台
          </Title>
          <Text type="secondary" style={{ marginLeft: 16 }}>
            基于 TextIn API 的文档解析与标签生成
          </Text>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            API设置
          </Button>
          <Button
            icon={<InfoCircleOutlined />}
            type="link"
            href="https://www.textin.com/document/pdf_to_markdown"
            target="_blank"
          >
            API文档
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: 1400, margin: '0 auto' }}>
          {/* API模式选择 */}
          <Card size="small" style={{ marginBottom: 24 }}>
            <Space>
              <Text strong>识别方式:</Text>
              <Radio.Group value={apiMode} onChange={(e) => handleApiModeChange(e.target.value)}>
                <Radio.Button value="textin">TextIn API</Radio.Button>
                <Radio.Button value="llm">大模型 API</Radio.Button>
              </Radio.Group>
            </Space>
          </Card>

          {/* API状态提示 */}
          {apiMode === 'textin' ? (
            !credentials.appId || !credentials.secretCode ? (
              <Alert
                message="请先配置 TextIn API 凭据"
                description="点击右上角的 API设置 按钮配置您的 TextIn API 凭据后才能使用识别功能。"
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
                action={
                  <Button size="small" onClick={() => setSettingsVisible(true)}>
                    立即配置
                  </Button>
                }
              />
            ) : (
              <Alert
                message="TextIn API 已配置"
                description="您可以开始上传文件进行识别了。"
                type="success"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )
          ) : (
            !llmConfig.apiKey ? (
              <Alert
                message="请先配置大模型 API 密钥"
                description="点击右上角的 API设置 按钮配置您的大模型 API 密钥后才能使用识别功能。"
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
                action={
                  <Button size="small" onClick={() => setSettingsVisible(true)}>
                    立即配置
                  </Button>
                }
              />
            ) : (
              <Alert
                message={`${llmVisionApi.getProviders().find(p => p.key === llmConfig.provider)?.name || '大模型'} API 已配置`}
                description="您可以开始上传文件进行识别了。"
                type="success"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )
          )}

          <Tabs
            defaultActiveKey="recognition"
            items={[
              {
                key: 'recognition',
                label: '图像识别',
                children: (
                  <Row gutter={[24, 24]}>
                    {/* 左侧：文件上传 */}
                    <Col xs={24} lg={8}>
                      <ImageUpload
                        onUploadSuccess={handleUploadSuccess}
                        onUploadError={handleUploadError}
                        loading={loading}
                        setLoading={setLoading}
                        apiMode={apiMode}
                      />
                    </Col>

                    {/* 右侧：结果展示 */}
                    <Col xs={24} lg={16}>
                      {convertedData ? (
                        <LabelRenderer
                          data={convertedData}
                          onDataChange={handleDataChange}
                        />
                      ) : (
                        <Card title="标签预览">
                          <div style={{
                            textAlign: 'center',
                            padding: '60px 20px',
                            color: '#999'
                          }}>
                            <InfoCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                            <Paragraph>
                              请上传图片或文档文件开始识别
                            </Paragraph>
                            <Text type="secondary">
                              支持 PNG、JPG、PDF 等格式，识别后将显示标签预览
                            </Text>
                          </div>
                        </Card>
                      )}
                    </Col>
                  </Row>
                )
              },
              {
                key: 'barcode-test',
                label: (
                  <span>
                    <BarcodeOutlined />
                    条形码测试
                  </span>
                ),
                children: <BarcodeTest />
              }
            ]}
          />

          {/* 数据下载区域 */}
          {(recognitionResult || convertedData) && (
            <>
              <Divider />
              <Card title="数据下载" style={{ marginTop: 24 }}>
                <Space wrap>
                  {recognitionResult && (
                    <Button
                      type="primary"
                      onClick={() => downloadJson(recognitionResult, 'textin-result.json')}
                    >
                      下载 TextIn 原始数据
                    </Button>
                  )}
                  {convertedData && (
                    <Button
                      type="primary"
                      onClick={() => downloadJson(convertedData, 'xprinter-data.json')}
                    >
                      下载 XPrinter 格式数据
                    </Button>
                  )}
                </Space>
              </Card>
            </>
          )}
        </div>
      </Content>

      <Footer style={{ textAlign: 'center', background: '#fafafa' }}>
        <Space split={<Divider type="vertical" />}>
          <Text type="secondary">
            图像识别测试平台 ©2024
          </Text>
          <Text type="secondary">
            基于 TextIn API 构建
          </Text>
          <a href="https://github.com" target="_blank" rel="noopener noreferrer">
            <GithubOutlined /> GitHub
          </a>
        </Space>
      </Footer>

      {/* API设置模态框 */}
      <Modal
        title="API 设置"
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        footer={null}
        width={600}
      >
        <Tabs
          items={[
            {
              key: 'textin',
              label: 'TextIn API',
              children: (
                <Form
                  layout="vertical"
                  initialValues={credentials}
                  onFinish={handleSaveCredentials}
                >
                  <Alert
                    message="获取 TextIn API 凭据"
                    description={
                      <div>
                        请登录 <a href="https://www.textin.com" target="_blank" rel="noopener noreferrer">TextIn 官网</a>，
                        前往 工作台-账号设置-开发者信息 查看您的 API 凭据。
                      </div>
                    }
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Form.Item
                    label="App ID"
                    name="appId"
                    rules={[{ required: true, message: '请输入 App ID' }]}
                  >
                    <Input placeholder="请输入您的 TextIn App ID" />
                  </Form.Item>

                  <Form.Item
                    label="Secret Code"
                    name="secretCode"
                    rules={[{ required: true, message: '请输入 Secret Code' }]}
                  >
                    <Input.Password placeholder="请输入您的 TextIn Secret Code" />
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" block>
                      保存 TextIn 配置
                    </Button>
                  </Form.Item>
                </Form>
              )
            },
            {
              key: 'llm',
              label: (
                <span>
                  <RobotOutlined />
                  大模型 API
                </span>
              ),
              children: (
                <Form
                  layout="vertical"
                  initialValues={{
                    provider: llmConfig.provider,
                    llmApiKey: llmConfig.apiKey,
                    sparkAppId: llmConfig.sparkAuth?.appId || '',
                    sparkApiSecret: llmConfig.sparkAuth?.apiSecret || '',
                    sparkApiKey: llmConfig.sparkAuth?.apiKey || ''
                  }}
                  onFinish={handleSaveCredentials}
                >
                  <Alert
                    message="大模型 API 配置"
                    description="选择您要使用的大模型提供商并配置相应的认证信息。星火大模型在识别中文文本样式方面表现优秀。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Form.Item
                    label="提供商"
                    name="provider"
                    rules={[{ required: true, message: '请选择提供商' }]}
                  >
                    <Select placeholder="选择大模型提供商">
                      {llmVisionApi.getProviders().map(provider => (
                        <Option key={provider.key} value={provider.key}>
                          {provider.name} ({provider.model})
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.provider !== currentValues.provider}>
                    {({ getFieldValue }) => {
                      const provider = getFieldValue('provider');

                      if (provider === 'spark') {
                        return (
                          <>
                            <Alert
                              message="星火认证信息"
                              description="星火大模型使用 APPID、APISecret 和 APIKey 进行认证。请从讯飞开放平台获取这些信息。"
                              type="success"
                              showIcon
                              style={{ marginBottom: 16 }}
                            />

                            <Form.Item
                              label="APPID"
                              name="sparkAppId"
                              rules={[{ required: true, message: '请输入 APPID' }]}
                            >
                              <Input placeholder="请输入星火 APPID" />
                            </Form.Item>

                            <Form.Item
                              label="APISecret"
                              name="sparkApiSecret"
                              rules={[{ required: true, message: '请输入 APISecret' }]}
                            >
                              <Input.Password placeholder="请输入星火 APISecret" />
                            </Form.Item>

                            <Form.Item
                              label="APIKey"
                              name="sparkApiKey"
                              rules={[{ required: true, message: '请输入 APIKey' }]}
                            >
                              <Input.Password placeholder="请输入星火 APIKey" />
                            </Form.Item>
                          </>
                        );
                      } else {
                        return (
                          <Form.Item
                            label="API 密钥"
                            name="llmApiKey"
                            rules={[{ required: true, message: '请输入 API 密钥' }]}
                          >
                            <Input.Password placeholder="请输入您的 API 密钥" />
                          </Form.Item>
                        );
                      }
                    }}
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" block>
                      保存大模型配置
                    </Button>
                  </Form.Item>
                </Form>
              )
            }
          ]}
        />
      </Modal>
    </Layout>
  );
}

export default App;
