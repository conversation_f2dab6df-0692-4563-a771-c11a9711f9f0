/**
 * 数据转换引擎
 * 将TextIn API返回的数据转换为XPrinter数据格式
 */

import barcodeReader from './barcodeReader.js';

// XPrinter标签类型常量
const ELEMENT_TYPES = {
  CANVAS: '3',           // 画布
  TEXT: '1',             // 文本
  BAR_CODE: '2',         // 一维码，条形码
  LINE: '4',             // 线条
  LOGO: '5',             // logo
  PICTURE: '6',          // 图片
  QR_CODE: '7',          // QR Code
  CIRCULAR: '8',         // 圆形
  TIME: '9',             // 时间
  TABLE: '10',           // 表格
  RECTANGLE: '11'        // 矩形
};

// 条码类型常量
const BARCODE_TYPES = {
  CODE_128: '1',
  CODE_39: '2',
  CODE_93: '3',
  EAN_13: '4',
  EAN_8: '5',
  ITF_25: '6',
  ITF_14: '7',
  UPC_A: '8',
  UPC_E: '9',
  CODABAR: '10',
  CHINA_POST: '11',
  MATRIX_25: '12',
  INDUSTRIAL_25: '13'
};

class DataConverter {
  constructor() {
    this.canvasWidth = 0;
    this.canvasHeight = 0;
    this.dpi = 144; // 默认DPI
  }

  /**
   * 根据条码内容智能识别条码类型
   * @param {string} content - 条码内容
   * @returns {string} 条码类型值
   */
  detectBarcodeType(content) {
    if (!content) return BARCODE_TYPES.CODE_128; // 默认

    let detectedType = BARCODE_TYPES.CODE_128;
    let typeName = 'CODE_128';

    // 精确匹配优先级更高的条码类型
    // EAN-13: 13位数字
    if (/^\d{13}$/.test(content)) {
      detectedType = BARCODE_TYPES.EAN_13;
      typeName = 'EAN_13';
    }
    // UPC-A: 12位数字
    else if (/^\d{12}$/.test(content)) {
      detectedType = BARCODE_TYPES.UPC_A;
      typeName = 'UPC_A';
    }
    // ITF-25: 10位或更多偶数位数字（优先于UPC-E）
    else if (/^\d{10,}$/.test(content) && content.length % 2 === 0) {
      detectedType = BARCODE_TYPES.ITF_25;
      typeName = 'ITF_25';
    }
    // EAN-8: 8位数字
    else if (/^\d{8}$/.test(content)) {
      detectedType = BARCODE_TYPES.EAN_8;
      typeName = 'EAN_8';
    }
    // UPC-E: 6-8位数字
    else if (/^\d{6,8}$/.test(content)) {
      detectedType = BARCODE_TYPES.UPC_E;
      typeName = 'UPC_E';
    }
    // CODE-39: 支持数字、大写字母和特殊字符
    else if (/^[0-9A-Z\-\.\s\$\/\+%]+$/.test(content)) {
      detectedType = BARCODE_TYPES.CODE_39;
      typeName = 'CODE_39';
    }
    // 其他数字序列默认使用CODE-128
    else if (/^\d+$/.test(content)) {
      detectedType = BARCODE_TYPES.CODE_128;
      typeName = 'CODE_128';
    }

    console.log(`检测到条码类型: ${typeName} (${detectedType}) for content: ${content}`);
    return detectedType;
  }

  /**
   * 转换TextIn数据为XPrinter格式
   * @param {Object} textinData - TextIn API返回的数据
   * @returns {Promise<Array>} XPrinter格式的数据数组
   */
  async convertToXPrinter(textinData) {
    try {
      console.log('开始转换TextIn数据:', textinData);
      
      if (!textinData.result || !textinData.result.pages) {
        throw new Error('TextIn数据格式不正确');
      }

      const result = [];
      const pages = textinData.result.pages;
      const processedImageIds = new Set(); // 记录已处理的图像ID，避免重复

      // 处理每一页（异步）
      for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
        const page = pages[pageIndex];
        console.log(`处理第${pageIndex + 1}页:`, page);

        // 设置画布尺寸
        if (page.width && page.height) {
          this.canvasWidth = page.width;
          this.canvasHeight = page.height;
        }

        // 创建画布元素
        if (pageIndex === 0) {
          const canvas = this.createCanvasElement();
          result.push(canvas);
        }

        // 处理页面内容（异步）
        if (page.content && Array.isArray(page.content)) {
          for (const item of page.content) {
            // 记录图像ID，避免重复处理
            if (item.type === 'image' && item.id) {
              processedImageIds.add(item.id);
              console.log(`记录已处理的图像ID: ${item.id}`);
            }

            const convertedItem = await this.convertContentItem(item);
            if (convertedItem) {
              result.push(convertedItem);
            }
          }
        }

        // 处理结构化数据（异步）- 跳过已处理的图像
        if (page.structured && Array.isArray(page.structured)) {
          for (const item of page.structured) {
            // 如果是图像块，检查是否已经处理过
            if (item.type === 'image' && item.content && Array.isArray(item.content)) {
              const hasProcessedImage = item.content.some(contentId =>
                processedImageIds.has(contentId)
              );
              if (hasProcessedImage) {
                console.log('跳过重复的图像块:', item);
                continue;
              }
            }

            const convertedItems = await this.convertStructuredItem(item, page.content);
            if (convertedItems && convertedItems.length > 0) {
              result.push(...convertedItems);
            }
          }
        }
      }

      console.log('转换完成，结果:', result);

      // 最终去重：移除位置和尺寸完全相同的重复元素
      const deduplicatedResult = this.removeDuplicateElements(result);
      console.log('去重后的结果:', deduplicatedResult);

      return deduplicatedResult;
    } catch (error) {
      console.error('数据转换失败:', error);
      throw new Error(`数据转换失败: ${error.message}`);
    }
  }

  /**
   * 创建画布元素
   * @returns {Object} 画布元素
   */
  createCanvasElement() {
    return {
      elementType: ELEMENT_TYPES.CANVAS,
      os: 'web',
      versionCode: 0,
      cableLabelDirection: 2,
      cableLabelLength: 0,
      templateBg: ""
    };
  }

  /**
   * 转换内容项
   * @param {Object} item - TextIn内容项
   * @returns {Promise<Object|null>} 转换后的XPrinter元素
   */
  async convertContentItem(item) {
    switch (item.type) {
      case 'line':
        return this.convertTextLine(item);
      case 'image':
        return await this.convertImage(item);
      default:
        console.warn('未知的内容类型:', item.type);
        return null;
    }
  }

  /**
   * 转换结构化项
   * @param {Object} item - TextIn结构化项
   * @param {Array} contentItems - 页面内容项数组
   * @returns {Array} 转换后的XPrinter元素数组
   */
  convertStructuredItem(item, contentItems = []) {
    switch (item.type) {
      case 'textblock':
        return this.convertTextBlock(item, contentItems);
      case 'table':
        return [this.convertTable(item)];
      case 'image':
        return [this.convertImageBlock(item, contentItems)];
      default:
        console.warn('未知的结构化类型:', item.type);
        return [];
    }
  }

  /**
   * 转换文本行
   * @param {Object} textLine - TextIn文本行数据
   * @returns {Object} XPrinter文本元素
   */
  convertTextLine(textLine) {
    const bounds = this.calculateBounds(textLine.pos);
    const text = textLine.text || '';

    // 计算合理的文字宽度，避免不必要的换行
    const adjustedWidth = this.calculateTextWidth(text, bounds.width, 12.0);

    return {
      elementType: 1, // 数字类型
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: adjustedWidth.toString(),
      height: bounds.height.toString(),
      rotationAngle: (textLine.angle || 0).toString(),
      content: text,
      textSize: '12.0',
      hAlignment: '1',
      bold: 'false',
      italic: 'false',
      underline: 'false',
      strikethrough: 'false',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      wordSpace: '0.0',
      linesSpace: '0.0',
      fontType: '-2',
      blackWhiteReflection: 'false',
      automaticHeightCalculation: 'true',
      lineWrap: 'true',
      flipX: 'false',
      // XPrinter必需字段
      inputDataType: '1',
      transmutationType: 1,
      transmutationValue: '1',
      transmutationCount: '0',
      transmutationNegativeNumbers: 'false',
      excelPos: -1,
      bindKey: '',
      controlType: '3',
      textArrangementType: 0,
      arcAngle: 180,
      prefix: '',
      suffix: '',
      showKeyName: false
    };
  }

  /**
   * 转换图像
   * @param {Object} image - TextIn图像数据
   * @returns {Promise<Object>} XPrinter元素
   */
  async convertImage(image) {
    const bounds = this.calculateBounds(image.pos);

    console.log(`转换图像: sub_type=${image.sub_type}, text=${image.text}, 位置=${JSON.stringify(bounds)}`);

    // 根据子类型确定元素类型和属性
    switch (image.sub_type) {
      case 'qrcode':
        return {
          elementType: 7, // QR_CODE
          x: bounds.x.toString(),
          y: bounds.y.toString(),
          width: bounds.width.toString(),
          height: bounds.height.toString(),
          rotationAngle: '0',
          lockLocation: 'false',
          takePrint: 'true',
          mirrorImage: 'false',
          codeType: 'QR_CODE',
          whiteMargin: '0',
          errorCorrectionLevel: 'M',
          content: image.text || '',
          inputDataType: '1',
          prefix: '',
          suffix: '',
          transmutationValue: '1',
          transmutationCount: '0',
          transmutationType: 1,
          transmutationNegativeNumbers: 'false',
          excelPos: -1,
          bindKey: '',
          showKeyName: false
        };
      case 'barcode':
        return await this.convertBarcode(image, bounds);
      case 'stamp':
      case 'chart':
      default:
        console.log(`图像转换为图片类型: sub_type=${image.sub_type}, text=${image.text}`);
        return {
          elementType: 6, // PICTURE
          x: bounds.x.toString(),
          y: bounds.y.toString(),
          width: bounds.width.toString(),
          height: bounds.height.toString(),
          rotationAngle: '0',
          lockLocation: 'false',
          takePrint: 'true',
          mirrorImage: 'false',
          content: image.data?.base64 || '',
          colorMode: '0',
          grayValue: '128',
          tile: 'false',
          blackWhiteReflection: 'false'
        };
    }
  }

  /**
   * 转换文本块
   * @param {Object} textBlock - TextIn文本块数据
   * @param {Array} contentItems - 内容项数组
   * @returns {Array} XPrinter文本元素数组
   */
  convertTextBlock(textBlock, contentItems) {
    const result = [];
    
    if (textBlock.content && Array.isArray(textBlock.content)) {
      textBlock.content.forEach(contentId => {
        const contentItem = contentItems.find(item => item.id === contentId);
        if (contentItem && contentItem.type === 'line') {
          const textElement = this.convertTextLine(contentItem);
          result.push(textElement);
        }
      });
    }

    return result;
  }

  /**
   * 转换表格
   * @param {Object} table - TextIn表格数据
   * @returns {Object} XPrinter表格元素
   */
  convertTable(table) {
    const bounds = this.calculateBounds(table.pos);

    // 转换单元格
    const cells = [];
    if (table.cells && Array.isArray(table.cells)) {
      table.cells.forEach(cell => {
        cells.push({
          row: cell.row.toString(),
          col: cell.col.toString(),
          rowSpan: (cell.row_span || 1).toString(),
          colSpan: (cell.col_span || 1).toString(),
          content: cell.text || '',
          hAlignment: '1',
          textSize: '12',
          bold: 'false',
          italic: 'false',
          underline: 'false',
          strikethrough: 'false',
          automaticHeightCalculation: true,
          lineWrap: 'true',
          horizontalAlignment: true,
          blackWhiteReflection: false,
          fontType: '0',
          wordSpace: '0',
          linesSpace: '0'
        });
      });
    }

    return {
      elementType: 10, // TABLE
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: bounds.width.toString(),
      height: bounds.height.toString(),
      rotationAngle: '0',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      rowHeights: table.rows_height || [],
      columnWidths: table.columns_width || [],
      borderWidth: '1',
      cells: cells
    };
  }

  /**
   * 转换条形码
   * @param {Object} image - TextIn条形码图像数据
   * @param {Object} bounds - 位置边界
   * @returns {Promise<Object>} XPrinter条形码元素
   */
  async convertBarcode(image, bounds) {
    let barcodeContent = image.text || '';

    // 如果没有文本内容但有base64图片，尝试识别条形码
    if (!barcodeContent && image.data && image.data.base64) {
      console.log('尝试从base64图片识别条形码内容...');
      try {
        barcodeContent = await barcodeReader.readFromBase64(image.data.base64);
        if (barcodeContent) {
          console.log(`成功识别条形码内容: ${barcodeContent}`);
        } else {
          console.warn('无法从图片中识别条形码内容，使用默认值');
          barcodeContent = '123456789'; // 默认值
        }
      } catch (error) {
        console.error('条形码识别失败:', error);
        barcodeContent = '123456789'; // 默认值
      }
    }

    const detectedBarcodeType = this.detectBarcodeType(barcodeContent);

    return {
      elementType: 2, // BAR_CODE
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: bounds.width.toString(),
      height: bounds.height.toString(),
      rotationAngle: '0',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      barcodeType: detectedBarcodeType,
      showText: '3',
      textAlignment: 1,
      content: barcodeContent,
      horizontalAlignment: 'true',
      inputDataType: '1',
      transmutationValue: '1',
      transmutationCount: '0',
      transmutationType: 1,
      transmutationNegativeNumbers: 'false',
      excelPos: -1,
      bindKey: '',
      showKeyName: false,
      textSize: '11.0',
      fontType: '-2',
      bold: 'false',
      italic: 'false',
      underline: 'false',
      strikethrough: 'false',
      controlType: '2',
      // 保存原始base64图片以备后用
      originalImage: image.data?.base64 || ''
    };
  }

  /**
   * 转换图像块
   * @param {Object} imageBlock - TextIn图像块数据
   * @param {Array} contentItems - 内容项数组
   * @returns {Object} XPrinter图片元素
   */
  convertImageBlock(imageBlock, contentItems) {
    const bounds = this.calculateBounds(imageBlock.pos);

    // 查找对应的图像内容
    let imageContent = '';
    if (imageBlock.content && Array.isArray(imageBlock.content)) {
      const imageItem = contentItems.find(item =>
        imageBlock.content.includes(item.id) && item.type === 'image'
      );
      if (imageItem && imageItem.data) {
        imageContent = imageItem.data.base64 || '';
      }
    }

    return {
      elementType: 6, // PICTURE
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: bounds.width.toString(),
      height: bounds.height.toString(),
      rotationAngle: '0',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      content: imageContent,
      colorMode: '0',
      grayValue: '128',
      tile: 'false',
      blackWhiteReflection: 'false'
    };
  }

  /**
   * 计算边界框
   * @param {Array} pos - 位置数组 [x1,y1,x2,y2,x3,y3,x4,y4]
   * @returns {Object} 边界框 {x, y, width, height}
   */
  calculateBounds(pos) {
    if (!pos || pos.length !== 8) {
      return { x: 0, y: 0, width: 100, height: 20 };
    }

    const xs = [pos[0], pos[2], pos[4], pos[6]];
    const ys = [pos[1], pos[3], pos[5], pos[7]];

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      x: Math.round(minX),
      y: Math.round(minY),
      width: Math.round(maxX - minX),
      height: Math.round(maxY - minY)
    };
  }

  /**
   * 计算合理的文字宽度，避免不必要的换行
   * @param {string} text - 文字内容
   * @param {number} originalWidth - 原始识别的宽度
   * @param {number} fontSize - 字体大小
   * @returns {number} 调整后的宽度
   */
  calculateTextWidth(text, originalWidth, fontSize = 12.0) {
    if (!text || text.length === 0) {
      return Math.max(originalWidth, 20); // 最小宽度20
    }

    // 估算字符宽度：中文字符约等于字体大小，英文字符约为字体大小的0.6倍
    const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const otherCharCount = text.length - chineseCharCount;

    const estimatedWidth = chineseCharCount * fontSize + otherCharCount * fontSize * 0.6;

    // 取原始宽度和估算宽度的较大值，并添加一些边距
    const adjustedWidth = Math.max(originalWidth, estimatedWidth * 1.1);

    // 设置最小和最大宽度限制
    const minWidth = 20;
    const maxWidth = this.canvasWidth * 0.8; // 不超过画布宽度的80%

    return Math.max(minWidth, Math.min(maxWidth, adjustedWidth));
  }

  /**
   * 移除重复元素
   * @param {Array} elements - 元素数组
   * @returns {Array} 去重后的元素数组
   */
  removeDuplicateElements(elements) {
    const seen = new Map();
    const result = [];

    for (const element of elements) {
      // 跳过画布元素
      if (element.elementType === '3' || element.elementType === 3) {
        result.push(element);
        continue;
      }

      // 创建唯一键：位置 + 尺寸
      const key = `${element.x}_${element.y}_${element.width}_${element.height}`;

      if (seen.has(key)) {
        const existingElement = seen.get(key);

        // 如果已存在的是条形码，保留条形码；如果新的是条形码，替换
        if (element.elementType === '2' || element.elementType === 2) {
          console.log(`替换重复元素为条形码: 位置(${element.x}, ${element.y})`);
          // 找到并替换已存在的元素
          const index = result.findIndex(item => item === existingElement);
          if (index !== -1) {
            result[index] = element;
          }
          seen.set(key, element);
        } else {
          console.log(`跳过重复元素: 位置(${element.x}, ${element.y}), 类型=${element.elementType}`);
        }
      } else {
        seen.set(key, element);
        result.push(element);
      }
    }

    console.log(`去重前: ${elements.length} 个元素, 去重后: ${result.length} 个元素`);
    return result;
  }
}

// 创建单例实例
const dataConverter = new DataConverter();

export default dataConverter;
