import React, { useState } from 'react';
import { Card, Input, Button, Space, message, Row, Col, Typography, Divider } from 'antd';
import LabelRenderer from '../components/LabelRenderer';

const { TextArea } = Input;
const { Title, Text } = Typography;

const TestRenderer = () => {
  const [canvasWidth, setCanvasWidth] = useState('300');
  const [canvasHeight, setCanvasHeight] = useState('200');
  const [jsonData, setJsonData] = useState('');
  const [renderData, setRenderData] = useState(null);
  const [loading, setLoading] = useState(false);

  // 示例数据 - 使用真实的XPrinter格式（毫米单位）
  const exampleData = [
    {
      "os": "android",
      "templateBg": "",
      "cableLabelDirection": 2,
      "cableLabelLength": 0,
      "elementType": "3",
      "versionCode": 0
    },
    {
      "lineWrap": "true",
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "prefix": "",
      "showKeyName": false,
      "transmutationValue": "1",
      "suffix": "",
      "italic": "false",
      "mirrorImage": "false",
      "content": "卡装1克马卡龙",
      "automaticHeightCalculation": "true",
      "textArrangementType": 0,
      "transmutationCount": "0",
      "strikethrough": "false",
      "flipX": "false",
      "transmutationNegativeNumbers": "false",
      "height": "5.046296",
      "inputDataType": "1",
      "linesSpace": "0.0",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "11.0",
      "bold": "false",
      "excelPos": -1,
      "arcAngle": 180,
      "bindKey": "",
      "controlType": "3",
      "rotationAngle": "0",
      "x": "7.4629717",
      "width": "28.37963",
      "y": "3.0184822",
      "hAlignment": "1",
      "elementType": 1,
      "blackWhiteReflection": "false",
      "wordSpace": "0.0"
    },
    {
      "lineWrap": "true",
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "prefix": "",
      "showKeyName": false,
      "transmutationValue": "1",
      "suffix": "",
      "italic": "false",
      "mirrorImage": "false",
      "content": "规格:1*30卡",
      "automaticHeightCalculation": "true",
      "textArrangementType": 0,
      "transmutationCount": "0",
      "strikethrough": "false",
      "flipX": "false",
      "transmutationNegativeNumbers": "false",
      "height": "5.509259",
      "inputDataType": "1",
      "linesSpace": "0.0",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "12.0",
      "bold": "false",
      "excelPos": -1,
      "arcAngle": 180,
      "bindKey": "",
      "controlType": "3",
      "rotationAngle": "0",
      "x": "8.972213",
      "width": "28.10185",
      "y": "7.722258",
      "hAlignment": "1",
      "elementType": 1,
      "blackWhiteReflection": "false",
      "wordSpace": "0.0"
    },
    {
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "showText": "3",
      "barcodeType": "4",
      "showKeyName": false,
      "transmutationValue": "1",
      "italic": "false",
      "mirrorImage": "false",
      "content": "6975370866829",
      "transmutationCount": "0",
      "strikethrough": "false",
      "transmutationNegativeNumbers": "false",
      "height": "13.564815",
      "inputDataType": "1",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "11.0",
      "bold": "false",
      "excelPos": -1,
      "bindKey": "",
      "horizontalAlignment": "true",
      "controlType": "2",
      "rotationAngle": "0",
      "textAlignment": 1,
      "x": "3.0740921",
      "width": "31.898148",
      "y": "14.2315",
      "elementType": 2
    }
  ];

  const handleRender = () => {
    if (!jsonData.trim()) {
      message.error('请输入JSON数据');
      return;
    }

    if (!canvasWidth || !canvasHeight) {
      message.error('请输入画布宽度和高度');
      return;
    }

    setLoading(true);
    
    try {
      const parsedData = JSON.parse(jsonData);

      // 支持两种格式：
      // 1. XPrinter原生格式：直接是元素数组
      // 2. 包装格式：{elements: [...]}
      let elements;
      if (Array.isArray(parsedData)) {
        // XPrinter原生格式
        elements = parsedData;
      } else if (parsedData.elements && Array.isArray(parsedData.elements)) {
        // 包装格式
        elements = parsedData.elements;
      } else {
        throw new Error('JSON数据必须是元素数组或包含elements数组的对象');
      }

      // 设置渲染配置
      const renderConfig = {
        elements: elements,
        canvasWidth: parseInt(canvasWidth),
        canvasHeight: parseInt(canvasHeight)
      };

      console.log('解析的数据:', renderConfig);
      setRenderData(renderConfig);
      message.success('渲染成功！');
    } catch (error) {
      console.error('JSON解析错误:', error);
      message.error(`JSON格式错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadExample = () => {
    setJsonData(JSON.stringify(exampleData, null, 2));

    // 从示例数据中获取或推算XPrinter画布尺寸（毫米）
    const canvasElement = exampleData.find(item => item.elementType === '3' || item.elementType === 3);
    let canvasWidthMm, canvasHeightMm;

    if (canvasElement && canvasElement.width && canvasElement.height) {
      canvasWidthMm = parseFloat(canvasElement.width);
      canvasHeightMm = parseFloat(canvasElement.height);
    } else {
      // 根据元素分布推算画布尺寸（毫米）
      const elements = exampleData.filter(item => item.elementType !== '3' && item.elementType !== 3);
      if (elements.length > 0) {
        const maxX = Math.max(...elements.map(el => parseFloat(el.x || 0) + parseFloat(el.width || 0)));
        const maxY = Math.max(...elements.map(el => parseFloat(el.y || 0) + parseFloat(el.height || 0)));
        canvasWidthMm = maxX + 5; // 添加5mm边距
        canvasHeightMm = maxY + 5;
      } else {
        canvasWidthMm = 50;
        canvasHeightMm = 30;
      }
    }

    // 设置Web显示尺寸（像素），可以是固定值或根据需要调整
    setCanvasWidth('400'); // Web显示宽度
    setCanvasHeight('300'); // Web显示高度

    message.success(`示例数据已加载！XPrinter画布: ${canvasWidthMm}mm × ${canvasHeightMm}mm`);
  };

  const clearData = () => {
    setJsonData('');
    setRenderData(null);
    setCanvasWidth('300');
    setCanvasHeight('200');
    message.success('数据已清空');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <Title level={2}>XPrinter 数据渲染测试</Title>
      <Text type="secondary">
        输入画布尺寸和XPrinter JSON数据，点击渲染按钮查看效果
      </Text>
      
      <Row gutter={[24, 24]} style={{ marginTop: '20px' }}>
        {/* 左侧输入区域 */}
        <Col xs={24} lg={12}>
          <Card title="输入参数" size="small">
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {/* 画布尺寸 */}
              <div>
                <Text strong>画布尺寸</Text>
                <Row gutter={8} style={{ marginTop: '8px' }}>
                  <Col span={12}>
                    <Input
                      placeholder="宽度"
                      value={canvasWidth}
                      onChange={(e) => setCanvasWidth(e.target.value)}
                      addonBefore="宽度"
                      type="number"
                    />
                  </Col>
                  <Col span={12}>
                    <Input
                      placeholder="高度"
                      value={canvasHeight}
                      onChange={(e) => setCanvasHeight(e.target.value)}
                      addonBefore="高度"
                      type="number"
                    />
                  </Col>
                </Row>
              </div>

              {/* JSON数据输入 */}
              <div>
                <Text strong>XPrinter JSON 数据</Text>
                <TextArea
                  value={jsonData}
                  onChange={(e) => setJsonData(e.target.value)}
                  placeholder="请输入XPrinter格式的JSON数据..."
                  rows={15}
                  style={{ marginTop: '8px', fontFamily: 'monospace' }}
                />
              </div>

              {/* 操作按钮 */}
              <Space>
                <Button 
                  type="primary" 
                  onClick={handleRender}
                  loading={loading}
                >
                  渲染
                </Button>
                <Button onClick={loadExample}>
                  加载示例
                </Button>
                <Button onClick={clearData}>
                  清空
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 右侧渲染区域 */}
        <Col xs={24} lg={12}>
          <Card title="渲染结果" size="small">
            {renderData ? (
              <div style={{ textAlign: 'center' }}>
                <LabelRenderer
                  data={renderData.elements}
                  canvasWidth={renderData.canvasWidth}
                  canvasHeight={renderData.canvasHeight}
                  onDataChange={() => {}} // 测试页面不需要数据变更
                />
                <Divider />
                <Space direction="vertical" size="small">
                  <Text type="secondary">
                    Web显示尺寸: {renderData.canvasWidth}px × {renderData.canvasHeight}px
                  </Text>
                  <Text type="secondary">
                    元素数量: {renderData.elements.length}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    注：XPrinter数据使用毫米(mm)单位，会自动映射到Web显示尺寸
                  </Text>
                </Space>
              </div>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                color: '#999'
              }}>
                <Text type="secondary">
                  请输入数据并点击渲染按钮查看效果
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TestRenderer;
