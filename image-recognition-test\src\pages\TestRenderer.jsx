import React, { useState } from 'react';
import { Card, Input, Button, Space, message, Row, Col, Typography, Divider } from 'antd';
import LabelRenderer from '../components/LabelRenderer';

const { TextArea } = Input;
const { Title, Text } = Typography;

const TestRenderer = () => {
  const [canvasWidth, setCanvasWidth] = useState('400');
  const [canvasHeight, setCanvasHeight] = useState('300');
  const [jsonData, setJsonData] = useState('');
  const [renderData, setRenderData] = useState(null);
  const [loading, setLoading] = useState(false);

  // 示例数据
  const exampleData = {
    "canvasWidth": 400,
    "canvasHeight": 300,
    "elements": [
      {
        "elementType": 3,
        "width": "400",
        "height": "300",
        "os": "web",
        "versionCode": 0,
        "cableLabelDirection": 2,
        "cableLabelLength": 0,
        "dpi": 203,
        "labelType": 1,
        "blackMarkHeight": 0,
        "blackMarkOffset": 0,
        "blackMarkDirection": 0,
        "templateBg": ""
      },
      {
        "elementType": 1,
        "x": "50",
        "y": "30",
        "width": "300",
        "height": "25",
        "rotationAngle": "0",
        "content": "火锅肥牛卷",
        "textSize": "18.0",
        "hAlignment": "2",
        "bold": "false",
        "italic": "false",
        "underline": "false",
        "strikethrough": "false",
        "lockLocation": "false",
        "takePrint": "true",
        "mirrorImage": "false",
        "wordSpace": "0.0",
        "linesSpace": "0.0",
        "fontType": "-2",
        "blackWhiteReflection": "false",
        "automaticHeightCalculation": "true",
        "lineWrap": "true",
        "flipX": "false",
        "inputDataType": "1",
        "transmutationType": 1,
        "transmutationValue": "1",
        "transmutationCount": "0",
        "transmutationNegativeNumbers": "false",
        "excelPos": -1,
        "bindKey": "",
        "controlType": "3",
        "textArrangementType": 0,
        "arcAngle": 180,
        "prefix": "",
        "suffix": "",
        "showKeyName": false
      },
      {
        "elementType": 1,
        "x": "50",
        "y": "70",
        "width": "150",
        "height": "20",
        "rotationAngle": "0",
        "content": "保质期：30天",
        "textSize": "12.0",
        "hAlignment": "1",
        "bold": "false",
        "italic": "false",
        "underline": "false",
        "strikethrough": "false",
        "lockLocation": "false",
        "takePrint": "true",
        "mirrorImage": "false",
        "wordSpace": "0.0",
        "linesSpace": "0.0",
        "fontType": "-2",
        "blackWhiteReflection": "false",
        "automaticHeightCalculation": "true",
        "lineWrap": "true",
        "flipX": "false",
        "inputDataType": "1",
        "transmutationType": 1,
        "transmutationValue": "1",
        "transmutationCount": "0",
        "transmutationNegativeNumbers": "false",
        "excelPos": -1,
        "bindKey": "",
        "controlType": "3",
        "textArrangementType": 0,
        "arcAngle": 180,
        "prefix": "",
        "suffix": "",
        "showKeyName": false
      },
      {
        "elementType": 2,
        "x": "50",
        "y": "150",
        "width": "300",
        "height": "60",
        "rotationAngle": "0",
        "content": "18818683102",
        "barcodeType": "1",
        "showText": "true",
        "textAlignment": "2",
        "lockLocation": "false",
        "takePrint": "true",
        "mirrorImage": "false",
        "inputDataType": "1",
        "transmutationType": 1,
        "transmutationValue": "1",
        "transmutationCount": "0",
        "transmutationNegativeNumbers": "false",
        "excelPos": -1,
        "bindKey": "",
        "controlType": "3"
      }
    ]
  };

  const handleRender = () => {
    if (!jsonData.trim()) {
      message.error('请输入JSON数据');
      return;
    }

    if (!canvasWidth || !canvasHeight) {
      message.error('请输入画布宽度和高度');
      return;
    }

    setLoading(true);
    
    try {
      const parsedData = JSON.parse(jsonData);
      
      // 验证数据结构
      if (!parsedData.elements || !Array.isArray(parsedData.elements)) {
        throw new Error('JSON数据必须包含elements数组');
      }

      // 设置画布尺寸
      const renderConfig = {
        ...parsedData,
        canvasWidth: parseInt(canvasWidth),
        canvasHeight: parseInt(canvasHeight)
      };

      setRenderData(renderConfig);
      message.success('渲染成功！');
    } catch (error) {
      console.error('JSON解析错误:', error);
      message.error(`JSON格式错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadExample = () => {
    setJsonData(JSON.stringify(exampleData, null, 2));
    setCanvasWidth(exampleData.canvasWidth.toString());
    setCanvasHeight(exampleData.canvasHeight.toString());
    message.success('示例数据已加载');
  };

  const clearData = () => {
    setJsonData('');
    setRenderData(null);
    setCanvasWidth('400');
    setCanvasHeight('300');
    message.success('数据已清空');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <Title level={2}>XPrinter 数据渲染测试</Title>
      <Text type="secondary">
        输入画布尺寸和XPrinter JSON数据，点击渲染按钮查看效果
      </Text>
      
      <Row gutter={[24, 24]} style={{ marginTop: '20px' }}>
        {/* 左侧输入区域 */}
        <Col xs={24} lg={12}>
          <Card title="输入参数" size="small">
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {/* 画布尺寸 */}
              <div>
                <Text strong>画布尺寸</Text>
                <Row gutter={8} style={{ marginTop: '8px' }}>
                  <Col span={12}>
                    <Input
                      placeholder="宽度"
                      value={canvasWidth}
                      onChange={(e) => setCanvasWidth(e.target.value)}
                      addonBefore="宽度"
                      type="number"
                    />
                  </Col>
                  <Col span={12}>
                    <Input
                      placeholder="高度"
                      value={canvasHeight}
                      onChange={(e) => setCanvasHeight(e.target.value)}
                      addonBefore="高度"
                      type="number"
                    />
                  </Col>
                </Row>
              </div>

              {/* JSON数据输入 */}
              <div>
                <Text strong>XPrinter JSON 数据</Text>
                <TextArea
                  value={jsonData}
                  onChange={(e) => setJsonData(e.target.value)}
                  placeholder="请输入XPrinter格式的JSON数据..."
                  rows={15}
                  style={{ marginTop: '8px', fontFamily: 'monospace' }}
                />
              </div>

              {/* 操作按钮 */}
              <Space>
                <Button 
                  type="primary" 
                  onClick={handleRender}
                  loading={loading}
                >
                  渲染
                </Button>
                <Button onClick={loadExample}>
                  加载示例
                </Button>
                <Button onClick={clearData}>
                  清空
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 右侧渲染区域 */}
        <Col xs={24} lg={12}>
          <Card title="渲染结果" size="small">
            {renderData ? (
              <div style={{ textAlign: 'center' }}>
                <LabelRenderer
                  data={renderData.elements}
                  canvasWidth={renderData.canvasWidth}
                  canvasHeight={renderData.canvasHeight}
                  onDataChange={() => {}} // 测试页面不需要数据变更
                />
                <Divider />
                <Text type="secondary">
                  画布尺寸: {renderData.canvasWidth} × {renderData.canvasHeight}
                </Text>
                <br />
                <Text type="secondary">
                  元素数量: {renderData.elements.length}
                </Text>
              </div>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                color: '#999'
              }}>
                <Text type="secondary">
                  请输入数据并点击渲染按钮查看效果
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TestRenderer;
