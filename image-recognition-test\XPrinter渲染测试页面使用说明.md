# XPrinter 渲染测试页面使用说明

## 功能介绍

新增的"渲染测试"页面允许您直接输入XPrinter格式的JSON数据进行渲染测试，无需上传图片或进行OCR识别。

## 访问方式

1. 打开应用主页面：http://localhost:5173
2. 点击顶部的"渲染测试"标签页
3. 或者直接访问该标签页

## 页面布局

### 左侧：输入参数区域

#### 1. 画布尺寸设置
- **宽度**：设置渲染画布的宽度（像素）
- **高度**：设置渲染画布的高度（像素）
- 默认值：400 × 300

#### 2. XPrinter JSON 数据输入
- 大文本框，支持输入完整的XPrinter格式JSON数据
- 使用等宽字体显示，便于阅读和编辑
- 支持多行输入

#### 3. 操作按钮
- **渲染**：解析JSON数据并在右侧显示渲染结果
- **加载示例**：加载预设的示例数据
- **清空**：清空所有输入数据和渲染结果

### 右侧：渲染结果区域

- 显示根据输入数据渲染的标签预览
- 包含画布尺寸和元素数量信息
- 支持元素编辑和交互

## JSON 数据格式

### 基本结构
```json
{
  "canvasWidth": 400,
  "canvasHeight": 300,
  "elements": [
    // 画布元素（必需）
    {
      "elementType": 3,
      "width": "400",
      "height": "300",
      // ... 其他画布属性
    },
    // 文本元素
    {
      "elementType": 1,
      "x": "50",
      "y": "30",
      "width": "300",
      "height": "25",
      "content": "文本内容",
      "textSize": "18.0",
      "hAlignment": "1",
      // ... 其他文本属性
    },
    // 条形码元素
    {
      "elementType": 2,
      "x": "50",
      "y": "150",
      "width": "300",
      "height": "60",
      "content": "123456789",
      "barcodeType": "1",
      "showText": "true",
      // ... 其他条形码属性
    }
  ]
}
```

### 元素类型说明

#### 画布元素 (elementType: 3)
- **必需元素**：每个JSON数据必须包含一个画布元素
- **关键属性**：
  - `width`: 画布宽度
  - `height`: 画布高度
  - `dpi`: 分辨率（可选）

#### 文本元素 (elementType: 1)
- **位置属性**：`x`, `y`, `width`, `height`
- **内容属性**：`content` (文本内容)
- **样式属性**：
  - `textSize`: 字体大小
  - `hAlignment`: 对齐方式 (1=左对齐, 2=居中, 3=右对齐)
  - `bold`, `italic`, `underline`: 字体样式

#### 条形码元素 (elementType: 2)
- **位置属性**：`x`, `y`, `width`, `height`
- **内容属性**：`content` (条形码内容)
- **格式属性**：
  - `barcodeType`: 条形码类型 (1=CODE128, 4=EAN13, 等)
  - `showText`: 是否显示文字 ("true"/"false")
  - `textAlignment`: 文字对齐方式

## 示例数据

点击"加载示例"按钮可以加载包含以下元素的示例数据：

1. **画布**：400×300像素的白色背景
2. **标题文字**："火锅肥牛卷" (居中对齐，18号字体)
3. **描述文字**："保质期：30天" (左对齐，12号字体)
4. **条形码**："18818683102" (CODE128格式，显示文字)

## 使用步骤

### 快速开始
1. 点击"加载示例"按钮
2. 点击"渲染"按钮
3. 查看右侧的渲染结果

### 自定义测试
1. 设置所需的画布尺寸
2. 在文本框中输入或粘贴XPrinter JSON数据
3. 点击"渲染"按钮
4. 查看渲染结果并进行调整

### 数据编辑
1. 在JSON文本框中直接编辑数据
2. 修改元素的位置、尺寸、内容等属性
3. 重新点击"渲染"查看更新后的效果

## 错误处理

### 常见错误及解决方法

1. **JSON格式错误**
   - 错误信息：`JSON格式错误: Unexpected token...`
   - 解决方法：检查JSON语法，确保括号、引号、逗号正确

2. **缺少elements数组**
   - 错误信息：`JSON数据必须包含elements数组`
   - 解决方法：确保JSON数据包含`elements`字段且为数组

3. **画布尺寸无效**
   - 错误信息：`请输入画布宽度和高度`
   - 解决方法：确保宽度和高度字段不为空且为有效数字

## 调试功能

### 控制台日志
打开浏览器开发者工具的控制台，可以查看详细的调试信息：
- 文本转换过程
- 条形码格式检测
- 画布尺寸设置
- 元素渲染参数

### 元素信息
鼠标悬停在渲染的元素上，可以查看元素的详细信息：
- 元素类型和内容
- 位置坐标和尺寸
- 字体大小等属性

## 应用场景

1. **数据格式验证**：测试XPrinter JSON数据的正确性
2. **布局调试**：调整元素位置和尺寸
3. **样式测试**：测试不同的字体、对齐方式等
4. **条形码测试**：验证不同格式的条形码渲染效果
5. **批量测试**：快速测试多组数据

## 注意事项

1. **数据格式**：必须严格遵循XPrinter JSON格式规范
2. **画布元素**：每个数据集必须包含一个画布元素
3. **坐标系统**：使用像素坐标，原点在左上角
4. **字符串格式**：数值属性通常以字符串形式存储
5. **元素顺序**：建议将画布元素放在数组的第一位

## 技术支持

如果遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 对比示例数据的格式
3. 检查JSON数据的语法正确性
4. 确认所有必需字段都已包含
