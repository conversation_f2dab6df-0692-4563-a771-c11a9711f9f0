<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字宽度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .text-element {
            border: 2px dashed #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
            padding: 5px;
            margin: 10px 0;
            position: relative;
            display: inline-block;
        }
        .info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .original {
            background-color: rgba(255, 0, 0, 0.1);
            border-color: #ff0000;
        }
        .adjusted {
            background-color: rgba(0, 255, 0, 0.1);
            border-color: #00ff00;
        }
    </style>
</head>
<body>
    <h1>文字宽度计算测试</h1>
    <p>这个页面用来测试文字宽度计算修复是否有效，避免不必要的换行。</p>

    <div id="test-results"></div>

    <script>
        // 模拟文字宽度计算函数
        function calculateTextWidth(text, originalWidth, fontSize = 12.0, canvasWidth = 800) {
            if (!text || text.length === 0) {
                return Math.max(originalWidth, 20);
            }

            // 估算字符宽度：中文字符约等于字体大小，英文字符约为字体大小的0.6倍
            const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
            const otherCharCount = text.length - chineseCharCount;
            
            const estimatedWidth = chineseCharCount * fontSize + otherCharCount * fontSize * 0.6;
            
            // 取原始宽度和估算宽度的较大值，并添加一些边距
            const adjustedWidth = Math.max(originalWidth, estimatedWidth * 1.1);
            
            // 设置最小和最大宽度限制
            const minWidth = 20;
            const maxWidth = canvasWidth * 0.8;
            
            return Math.max(minWidth, Math.min(maxWidth, adjustedWidth));
        }

        // 测试用例
        const testCases = [
            {
                text: '短文字',
                originalWidth: 50,
                description: '短文字测试'
            },
            {
                text: '这是中等长度的文字内容',
                originalWidth: 80,
                description: '中等长度文字测试'
            },
            {
                text: '这是一段很长很长的中文文字内容，用来测试宽度计算是否正确，避免不必要的换行',
                originalWidth: 120,
                description: '长文字测试'
            },
            {
                text: 'Short English text',
                originalWidth: 60,
                description: '英文短文字测试'
            },
            {
                text: 'This is a longer English text to test width calculation and prevent unnecessary line wrapping',
                originalWidth: 100,
                description: '英文长文字测试'
            },
            {
                text: '中英混合 Mixed Chinese and English text 测试宽度计算',
                originalWidth: 90,
                description: '中英混合文字测试'
            }
        ];

        // 渲染测试结果
        function renderTests() {
            const container = document.getElementById('test-results');
            
            testCases.forEach((testCase, index) => {
                const adjustedWidth = calculateTextWidth(testCase.text, testCase.originalWidth);
                
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <h3>测试 ${index + 1}: ${testCase.description}</h3>
                    <p><strong>文字内容:</strong> "${testCase.text}"</p>
                    
                    <div>
                        <h4>原始宽度 (${testCase.originalWidth}px):</h4>
                        <div class="text-element original" style="width: ${testCase.originalWidth}px; font-size: 12px;">
                            ${testCase.text}
                        </div>
                        <div class="info">宽度: ${testCase.originalWidth}px</div>
                    </div>
                    
                    <div>
                        <h4>调整后宽度 (${Math.round(adjustedWidth)}px):</h4>
                        <div class="text-element adjusted" style="width: ${Math.round(adjustedWidth)}px; font-size: 12px;">
                            ${testCase.text}
                        </div>
                        <div class="info">宽度: ${Math.round(adjustedWidth)}px (增加了 ${Math.round(adjustedWidth - testCase.originalWidth)}px)</div>
                    </div>
                `;
                
                container.appendChild(testDiv);
            });
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', renderTests);
    </script>
</body>
</html>
